'use client'

import { useEffect, useState } from 'react'

export function CursorFollower() {
  const [mounted, setMounted] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [followerPosition, setFollowerPosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', updateMousePosition)

    return () => {
      window.removeEventListener('mousemove', updateMousePosition)
    }
  }, [mounted])

  useEffect(() => {
    if (!mounted) return

    const animateFollower = () => {
      setFollowerPosition(prev => ({
        x: prev.x + (mousePosition.x - prev.x) * 0.15,
        y: prev.y + (mousePosition.y - prev.y) * 0.15,
      }))
    }

    const intervalId = setInterval(animateFollower, 16) // ~60fps

    return () => clearInterval(intervalId)
  }, [mousePosition, mounted])

  if (!mounted) return null

  return (
    <>
      {/* Main Cursor */}
      <div
        className="fixed w-2 h-2 bg-primary rounded-full pointer-events-none z-[9999] mix-blend-difference transition-transform duration-100"
        style={{
          left: mousePosition.x - 4,
          top: mousePosition.y - 4,
        }}
      />
      
      {/* Follower */}
      <div
        className="fixed w-8 h-8 border-2 border-primary/50 rounded-full pointer-events-none z-[9998] transition-transform duration-300"
        style={{
          left: followerPosition.x - 16,
          top: followerPosition.y - 16,
        }}
      />
    </>
  )
}

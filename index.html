<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APCEM Agency - Premium Digital Solutions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        gold: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        }
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'fade-in': 'fadeIn 1s ease-out',
                    }
                }
            }
        }
    </script>

</head>
<body class="bg-black text-white overflow-x-hidden">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-effect-strong modern-nav">
        <div class="container mx-auto px-6 py-3">
            <div class="flex justify-between items-center">
                <!-- Logo Section -->
                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <img src="images/logo-apcem.jpg" alt="APCEM Logo" class="logo-nav nav-logo">
                    <div class="text-2xl font-bold gradient-text">APCEM</div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
                    <a href="#home" class="nav-link text-gray-300 hover:text-gold-400 font-medium">
                        <span class="en-text">Home</span>
                        <span class="ar-text hidden">الرئيسية</span>
                    </a>
                    <a href="#services" class="nav-link text-gray-300 hover:text-gold-400 font-medium">
                        <span class="en-text">Services</span>
                        <span class="ar-text hidden">الخدمات</span>
                    </a>
                    <a href="#portfolio" class="nav-link text-gray-300 hover:text-gold-400 font-medium">
                        <span class="en-text">Portfolio</span>
                        <span class="ar-text hidden">الأعمال</span>
                    </a>
                    <a href="#about" class="nav-link text-gray-300 hover:text-gold-400 font-medium">
                        <span class="en-text">About</span>
                        <span class="ar-text hidden">عن الشركة</span>
                    </a>
                    <a href="#contact" class="nav-link text-gray-300 hover:text-gold-400 font-medium">
                        <span class="en-text">Contact</span>
                        <span class="ar-text hidden">تواصل</span>
                    </a>
                </div>

                <!-- Right Section -->
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Language Toggle -->
                    <button id="langToggle" class="lang-toggle px-4 py-2 text-gold-400 rounded-lg font-medium transition-all duration-300">
                        <span class="en-text">العربية</span>
                        <span class="ar-text hidden">English</span>
                    </button>

                    <!-- CTA Button -->
                    <a href="#contact" class="btn-primary text-black px-6 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hidden sm:block">
                        <span class="en-text relative z-10">Get Started</span>
                        <span class="ar-text hidden relative z-10">ابدأ الآن</span>
                    </a>

                    <!-- Mobile Menu Button -->
                    <button id="mobileMenuBtn" class="lg:hidden text-gold-400 hover:text-gold-300 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu lg:hidden fixed top-0 right-0 w-80 h-full glass-effect-strong z-50">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <div class="flex items-center space-x-3">
                        <img src="images/logo-apcem.jpg" alt="APCEM Logo" class="w-8 h-8 rounded-lg">
                        <span class="text-xl font-bold gradient-text">APCEM</span>
                    </div>
                    <button id="closeMobileMenu" class="text-gold-400 hover:text-gold-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="space-y-6">
                    <a href="#home" class="block text-gray-300 hover:text-gold-400 font-medium py-2 mobile-nav-link">
                        <span class="en-text">Home</span>
                        <span class="ar-text hidden">الرئيسية</span>
                    </a>
                    <a href="#services" class="block text-gray-300 hover:text-gold-400 font-medium py-2 mobile-nav-link">
                        <span class="en-text">Services</span>
                        <span class="ar-text hidden">الخدمات</span>
                    </a>
                    <a href="#portfolio" class="block text-gray-300 hover:text-gold-400 font-medium py-2 mobile-nav-link">
                        <span class="en-text">Portfolio</span>
                        <span class="ar-text hidden">الأعمال</span>
                    </a>
                    <a href="#about" class="block text-gray-300 hover:text-gold-400 font-medium py-2 mobile-nav-link">
                        <span class="en-text">About</span>
                        <span class="ar-text hidden">عن الشركة</span>
                    </a>
                    <a href="#contact" class="block text-gray-300 hover:text-gold-400 font-medium py-2 mobile-nav-link">
                        <span class="en-text">Contact</span>
                        <span class="ar-text hidden">تواصل</span>
                    </a>

                    <div class="pt-6 border-t border-gold-500/20">
                        <a href="#contact" class="btn-primary text-black px-6 py-3 rounded-lg font-semibold w-full text-center block">
                            <span class="en-text relative z-10">Get Started</span>
                            <span class="ar-text hidden relative z-10">ابدأ الآن</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden grid-overlay">
        <!-- Particles Background -->
        <div class="particles absolute inset-0">
            <div class="particle w-2 h-2" style="left: 10%; animation-delay: 0s; animation-duration: 15s;"></div>
            <div class="particle w-1 h-1" style="left: 20%; animation-delay: 2s; animation-duration: 18s;"></div>
            <div class="particle w-3 h-3" style="left: 30%; animation-delay: 4s; animation-duration: 12s;"></div>
            <div class="particle w-1 h-1" style="left: 40%; animation-delay: 6s; animation-duration: 20s;"></div>
            <div class="particle w-2 h-2" style="left: 50%; animation-delay: 8s; animation-duration: 16s;"></div>
            <div class="particle w-1 h-1" style="left: 60%; animation-delay: 10s; animation-duration: 14s;"></div>
            <div class="particle w-2 h-2" style="left: 70%; animation-delay: 12s; animation-duration: 22s;"></div>
            <div class="particle w-1 h-1" style="left: 80%; animation-delay: 14s; animation-duration: 18s;"></div>
            <div class="particle w-3 h-3" style="left: 90%; animation-delay: 16s; animation-duration: 15s;"></div>
        </div>

        <!-- Animated Background Blobs -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-gold-500 to-gold-700 rounded-full mix-blend-multiply filter blur-3xl opacity-20 morphing-blob floating-element"></div>
            <div class="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-gold-600 to-gold-800 rounded-full mix-blend-multiply filter blur-3xl opacity-25 morphing-blob floating-element" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 left-1/2 w-72 h-72 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full mix-blend-multiply filter blur-3xl opacity-15 morphing-blob floating-element" style="animation-delay: 4s;"></div>
            <div class="absolute top-1/2 left-10 w-64 h-64 bg-gradient-to-br from-gold-300 to-gold-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 morphing-blob floating-element" style="animation-delay: 6s;"></div>
        </div>

        <!-- Main Content -->
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center">
                <!-- Logo -->
                <div class="mb-12 animate-fade-in">
                    <img src="images/logo-apcem.jpg" alt="APCEM Agency Logo" class="logo-main mx-auto">
                </div>

                <!-- Main Heading -->
                <div class="mb-8">
                    <h1 class="text-5xl md:text-7xl lg:text-8xl font-black mb-4 animate-slide-up leading-tight">
                        <span class="en-text en-font block mb-2">APCEM</span>
                        <span class="ar-text ar-font hidden block mb-2">أبسيم</span>
                        <span class="gradient-text block">
                            <span class="en-text en-font">AGENCY</span>
                            <span class="ar-text ar-font hidden">وكالة</span>
                        </span>
                    </h1>

                    <!-- Decorative Line -->
                    <div class="w-32 h-1 bg-gradient-to-r from-gold-400 to-gold-600 mx-auto rounded-full animate-fade-in" style="animation-delay: 0.2s;"></div>
                </div>

                <!-- Subtitle -->
                <div class="mb-12">
                    <p class="text-xl md:text-2xl lg:text-3xl text-gray-300 max-w-5xl mx-auto animate-fade-in leading-relaxed" style="animation-delay: 0.3s;">
                        <span class="en-text en-font">Crafting Premium Digital Experiences with Cutting-Edge Innovation</span>
                        <span class="ar-text ar-font hidden">نصنع تجارب رقمية متميزة بأحدث التقنيات المبتكرة</span>
                    </p>
                </div>

                <!-- CTA Buttons -->
                <div class="mb-16">
                    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in" style="animation-delay: 0.6s;">
                        <button class="btn-primary text-black px-12 py-5 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl">
                            <span class="en-text en-font relative z-10">Explore Our Work</span>
                            <span class="ar-text ar-font hidden relative z-10">استكشف أعمالنا</span>
                        </button>
                        <button class="neon-border text-gold-400 px-12 py-5 rounded-xl font-bold text-lg hover:bg-gold-500 hover:text-black transition-all duration-300 transform hover:scale-105 relative overflow-hidden">
                            <span class="en-text en-font relative z-10">Contact Us</span>
                            <span class="ar-text ar-font hidden relative z-10">تواصل معنا</span>
                        </button>
                    </div>
                </div>

                <!-- Stats Section -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16 animate-fade-in" style="animation-delay: 0.8s;">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">50+</div>
                        <div class="text-gray-400 text-sm md:text-base">
                            <span class="en-text">Projects</span>
                            <span class="ar-text hidden">مشروع</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">100%</div>
                        <div class="text-gray-400 text-sm md:text-base">
                            <span class="en-text">Satisfaction</span>
                            <span class="ar-text hidden">رضا العملاء</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">24/7</div>
                        <div class="text-gray-400 text-sm md:text-base">
                            <span class="en-text">Support</span>
                            <span class="ar-text hidden">الدعم</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">5+</div>
                        <div class="text-gray-400 text-sm md:text-base">
                            <span class="en-text">Years</span>
                            <span class="ar-text hidden">سنوات</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
                <div class="w-6 h-10 border-2 border-gold-400 rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-gold-400 rounded-full mt-2 animate-pulse"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-32 relative">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20">
                <div class="inline-block px-6 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 rounded-full border border-gold-500/30 mb-6">
                    <span class="text-gold-400 font-semibold">
                        <span class="en-text">What We Do</span>
                        <span class="ar-text hidden">ما نقدمه</span>
                    </span>
                </div>
                <h2 class="text-5xl md:text-7xl font-black mb-6 gradient-text">
                    <span class="en-text en-font">Our Services</span>
                    <span class="ar-text ar-font hidden">خدماتنا</span>
                </h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    <span class="en-text en-font">We deliver exceptional digital solutions that transform your business and elevate your brand to new heights</span>
                    <span class="ar-text ar-font hidden">نقدم حلولاً رقمية استثنائية تحول أعمالك وترفع علامتك التجارية إلى آفاق جديدة</span>
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
                <!-- Service 1 -->
                <div class="group glass-effect p-8 rounded-3xl hover:transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/5 to-gold-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10 text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl mb-8 flex items-center justify-center morphing-blob mx-auto group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-black" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">Web Development</span>
                            <span class="ar-text ar-font hidden">تطوير المواقع</span>
                        </h3>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            <span class="en-text en-font">Modern, responsive websites built with cutting-edge technologies and optimized for performance</span>
                            <span class="ar-text ar-font hidden">مواقع حديثة ومتجاوبة مبنية بأحدث التقنيات ومحسنة للأداء</span>
                        </p>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">React</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Next.js</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Node.js</span>
                        </div>
                    </div>
                </div>

                <!-- Service 2 -->
                <div class="group glass-effect p-8 rounded-3xl hover:transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/5 to-gold-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10 text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl mb-8 flex items-center justify-center morphing-blob mx-auto group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-black" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">Brand Design</span>
                            <span class="ar-text ar-font hidden">تصميم العلامة التجارية</span>
                        </h3>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            <span class="en-text en-font">Stunning visual identities that make your brand unforgettable and stand out from the competition</span>
                            <span class="ar-text ar-font hidden">هويات بصرية مذهلة تجعل علامتك التجارية لا تُنسى وتميزها عن المنافسين</span>
                        </p>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Logo Design</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">UI/UX</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Branding</span>
                        </div>
                    </div>
                </div>

                <!-- Service 3 -->
                <div class="group glass-effect p-8 rounded-3xl hover:transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/5 to-gold-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10 text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-gold-500 to-gold-600 rounded-2xl mb-8 flex items-center justify-center morphing-blob mx-auto group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-black" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">Digital Marketing</span>
                            <span class="ar-text ar-font hidden">التسويق الرقمي</span>
                        </h3>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            <span class="en-text en-font">Strategic campaigns that drive growth, engagement, and measurable results for your business</span>
                            <span class="ar-text ar-font hidden">حملات استراتيجية تحقق النمو والتفاعل ونتائج قابلة للقياس لأعمالك</span>
                        </p>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">SEO</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Social Media</span>
                            <span class="px-3 py-1 bg-gold-500/20 text-gold-400 rounded-full text-sm">Analytics</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="py-20 relative">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">
                        <span class="en-text en-font">About APCEM</span>
                        <span class="ar-text ar-font hidden">عن أبسيم</span>
                    </h2>
                    <p class="text-xl text-gray-300 mb-6">
                        <span class="en-text en-font">We are a premium digital agency specializing in creating extraordinary online experiences. Our team combines creativity with cutting-edge technology to deliver solutions that drive results.</span>
                        <span class="ar-text ar-font hidden">نحن وكالة رقمية متميزة متخصصة في إنشاء تجارب استثنائية عبر الإنترنت. يجمع فريقنا بين الإبداع والتكنولوجيا المتطورة لتقديم حلول تحقق النتائج.</span>
                    </p>
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gold-400">50+</div>
                            <div class="text-gray-400">
                                <span class="en-text en-font">Projects</span>
                                <span class="ar-text ar-font hidden">مشروع</span>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gold-400">100%</div>
                            <div class="text-gray-400">
                                <span class="en-text en-font">Satisfaction</span>
                                <span class="ar-text ar-font hidden">رضا العملاء</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <div class="glass-effect p-8 rounded-2xl">
                        <div class="w-full h-64 bg-gradient-to-br from-gold-500 to-gold-700 rounded-lg flex items-center justify-center">
                            <div class="text-6xl font-bold text-black">APCEM</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-32 relative overflow-hidden">
        <div class="absolute inset-0 grid-overlay opacity-30"></div>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-20">
                <div class="inline-block px-6 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 rounded-full border border-gold-500/30 mb-6">
                    <span class="text-gold-400 font-semibold">
                        <span class="en-text">Our Portfolio</span>
                        <span class="ar-text hidden">معرض أعمالنا</span>
                    </span>
                </div>
                <h2 class="text-5xl md:text-7xl font-black mb-6 gradient-text">
                    <span class="en-text en-font">Featured Work</span>
                    <span class="ar-text ar-font hidden">أعمال مميزة</span>
                </h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    <span class="en-text en-font">Discover our latest projects that showcase innovation, creativity, and technical excellence</span>
                    <span class="ar-text ar-font hidden">اكتشف أحدث مشاريعنا التي تعرض الابتكار والإبداع والتميز التقني</span>
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
                <!-- Project 1 -->
                <div class="group glass-effect rounded-3xl overflow-hidden hover:transform hover:scale-105 transition-all duration-500 relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/10 to-gold-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="h-64 bg-gradient-to-br from-gold-500 to-gold-700 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">React</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Node.js</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">MongoDB</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">E-Commerce Platform</span>
                            <span class="ar-text ar-font hidden">منصة التجارة الإلكترونية</span>
                        </h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">
                            <span class="en-text en-font">Modern online store with advanced features, payment integration, and real-time analytics</span>
                            <span class="ar-text ar-font hidden">متجر إلكتروني حديث بميزات متقدمة وتكامل المدفوعات والتحليلات الفورية</span>
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-400">2024</span>
                            <button class="text-gold-400 hover:text-gold-300 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="group glass-effect rounded-3xl overflow-hidden hover:transform hover:scale-105 transition-all duration-500 relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/10 to-gold-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="h-64 bg-gradient-to-br from-gold-600 to-gold-800 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Next.js</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">TypeScript</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Prisma</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">Corporate Website</span>
                            <span class="ar-text ar-font hidden">موقع الشركة</span>
                        </h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">
                            <span class="en-text en-font">Professional business presence with CMS integration and SEO optimization</span>
                            <span class="ar-text ar-font hidden">حضور مهني للأعمال مع تكامل نظام إدارة المحتوى وتحسين محركات البحث</span>
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-400">2024</span>
                            <button class="text-gold-400 hover:text-gold-300 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Project 3 -->
                <div class="group glass-effect rounded-3xl overflow-hidden hover:transform hover:scale-105 transition-all duration-500 relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold-500/10 to-gold-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="h-64 bg-gradient-to-br from-gold-400 to-gold-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">React Native</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Firebase</span>
                                <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Redux</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gold-400 group-hover:text-gold-300 transition-colors">
                            <span class="en-text en-font">Mobile App</span>
                            <span class="ar-text ar-font hidden">تطبيق الجوال</span>
                        </h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">
                            <span class="en-text en-font">Cross-platform mobile solution with real-time features and cloud integration</span>
                            <span class="ar-text ar-font hidden">حل متعدد المنصات للجوال مع ميزات فورية وتكامل سحابي</span>
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-400">2024</span>
                            <button class="text-gold-400 hover:text-gold-300 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <button class="btn-primary text-black px-10 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105">
                    <span class="en-text en-font relative z-10">View All Projects</span>
                    <span class="ar-text ar-font hidden relative z-10">عرض جميع المشاريع</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 relative">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl md:text-6xl font-bold text-center mb-16 gradient-text">
                <span class="en-text en-font">Get In Touch</span>
                <span class="ar-text ar-font hidden">تواصل معنا</span>
            </h2>
            <div class="max-w-4xl mx-auto">
                <div class="glass-effect p-8 rounded-2xl">
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-bold mb-6 text-gold-400">
                                <span class="en-text en-font">Let's Create Something Amazing</span>
                                <span class="ar-text ar-font hidden">لننشئ شيئًا مذهلاً</span>
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <div class="w-12 h-12 bg-gold-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left rtl:text-right">
                                        <div class="text-gold-400 font-semibold">Email</div>
                                        <div class="text-gray-300"><EMAIL></div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <div class="w-12 h-12 bg-gold-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left rtl:text-right">
                                        <div class="text-gold-400 font-semibold">
                                            <span class="en-text en-font">Phone</span>
                                            <span class="ar-text ar-font hidden">الهاتف</span>
                                        </div>
                                        <div class="text-gray-300">+****************</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                    <div class="w-12 h-12 bg-gold-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.748-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                                        </svg>
                                    </div>
                                    <div class="text-left rtl:text-right">
                                        <div class="text-gold-400 font-semibold">Instagram</div>
                                        <div class="text-gray-300">@apcem_agency</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <form class="space-y-6">
                                <div>
                                    <input type="text" placeholder="Your Name / اسمك" class="w-full bg-black border border-gold-500 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-gold-400 transition-colors">
                                </div>
                                <div>
                                    <input type="email" placeholder="Your Email / بريدك الإلكتروني" class="w-full bg-black border border-gold-500 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-gold-400 transition-colors">
                                </div>
                                <div>
                                    <textarea rows="4" placeholder="Your Message / رسالتك" class="w-full bg-black border border-gold-500 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-gold-400 transition-colors resize-none"></textarea>
                                </div>
                                <button type="submit" class="w-full bg-gradient-to-r from-gold-500 to-gold-600 text-black py-3 rounded-lg font-bold hover:from-gold-400 hover:to-gold-500 transition-all duration-300 transform hover:scale-105">
                                    <span class="en-text en-font">Send Message</span>
                                    <span class="ar-text ar-font hidden">إرسال الرسالة</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 border-t border-gold-500/20">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <div class="text-3xl font-bold gradient-text mb-4">APCEM AGENCY</div>
                <p class="text-gray-400 mb-6">
                    <span class="en-text en-font">Crafting Premium Digital Experiences</span>
                    <span class="ar-text ar-font hidden">نصنع تجارب رقمية متميزة</span>
                </p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gold-400 hover:text-gold-300 transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gold-400 hover:text-gold-300 transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.748-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gold-400 hover:text-gold-300 transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                    </a>
                </div>
                <div class="mt-8 text-gray-500 text-sm">
                    <span class="en-text en-font">© 2024 APCEM Agency. All rights reserved.</span>
                    <span class="ar-text ar-font hidden">© 2024 وكالة أبسيم. جميع الحقوق محفوظة.</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

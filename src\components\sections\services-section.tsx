'use client'

import { useLanguage } from '@/components/providers/language-provider'
import { Code, Palette, TrendingUp } from 'lucide-react'

export function ServicesSection() {
  const { t } = useLanguage()

  const services = [
    {
      icon: Code,
      title: { en: 'Web Development', ar: 'تطوير المواقع' },
      description: { en: 'Modern, responsive websites built with cutting-edge technologies', ar: 'مواقع حديثة ومتجاوبة مبنية بأحدث التقنيات' },
      technologies: ['React', 'Next.js', 'Node.js']
    },
    {
      icon: Palette,
      title: { en: 'Brand Design', ar: 'تصميم العلامة التجارية' },
      description: { en: 'Stunning visual identities that make your brand unforgettable', ar: 'هويات بصرية مذهلة تجعل علامتك التجارية لا تُنسى' },
      technologies: ['Logo Design', 'UI/UX', 'Branding']
    },
    {
      icon: TrendingUp,
      title: { en: 'Digital Marketing', ar: 'التسويق الرقمي' },
      description: { en: 'Strategic campaigns that drive growth and engagement', ar: 'حملات استراتيجية تحقق النمو والتفاعل' },
      technologies: ['SEO', 'Social Media', 'Analytics']
    }
  ]

  return (
    <section id="services" className="py-32 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <div className="inline-block px-6 py-2 glass-effect rounded-full border border-primary/30 mb-6">
            <span className="text-primary font-semibold">
              {t('services-badge', { en: 'What We Do', ar: 'ما نقدمه' })}
            </span>
          </div>
          <h2 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
            {t('services-title', { en: 'Our Services', ar: 'خدماتنا' })}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {t('services-subtitle', {
              en: 'We deliver exceptional digital solutions that transform your business',
              ar: 'نقدم حلولاً رقمية استثنائية تحول أعمالك'
            })}
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {services.map((service, index) => {
            const Icon = service.icon
            return (
              <div key={index} className="group glass-effect p-8 rounded-3xl hover:scale-105 transition-all duration-300 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10 text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary/80 rounded-2xl mb-8 flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-10 h-10 text-black" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-primary group-hover:text-primary/80 transition-colors">
                    {t(`service-${index}-title`, service.title)}
                  </h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {t(`service-${index}-description`, service.description)}
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {service.technologies.map((tech, techIndex) => (
                      <span key={techIndex} className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useTheme } from '@/components/providers/theme-provider'
import { useLanguage } from '@/components/providers/language-provider'
import { Moon, Sun, Menu, X } from 'lucide-react'
import { cn } from '@/lib/utils'

export function Navigation() {
  const { theme, setTheme } = useTheme()
  const { language, setLanguage, t } = useLanguage()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'ar' : 'en')
  }

  const navLinks = [
    { href: '#home', label: { en: 'Home', ar: 'الرئيسية' } },
    { href: '#services', label: { en: 'Services', ar: 'الخدمات' } },
    { href: '/portfolio', label: { en: 'Portfolio', ar: 'الأعمال' } },
    { href: '#about', label: { en: 'About', ar: 'عن الشركة' } },
    { href: '#contact', label: { en: 'Contact', ar: 'تواصل' } },
  ]

  return (
    <>
      <nav className={cn(
        'fixed top-0 w-full z-[100] transition-all duration-300',
        'glass-effect-strong modern-nav',
        isScrolled && 'backdrop-blur-3xl'
      )}>
        <div className="container mx-auto px-6 py-2">
          <div className="flex justify-between items-center">
            {/* Logo Section */}
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
              <Image
                src="/images/logo-apcem.jpg"
                alt="APCEM Logo"
                width={32}
                height={32}
                className="rounded-lg object-cover transition-transform duration-300 hover:scale-110 hover:rotate-3"
              />
              <span className="text-xl font-bold gradient-text">APCEM</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="nav-link text-muted-foreground hover:text-primary font-medium text-sm transition-all duration-300 relative group"
                >
                  {t('nav-' + link.href.replace('#', ''), link.label)}
                  <span className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-4/5 group-hover:left-[10%]" />
                </Link>
              ))}
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="theme-toggle p-2 text-primary rounded-lg transition-all duration-300 hover:bg-primary/10 hover:scale-110"
                aria-label="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Moon className="w-5 h-5" />
                ) : (
                  <Sun className="w-5 h-5" />
                )}
              </button>

              {/* Language Toggle */}
              <button
                onClick={toggleLanguage}
                className="lang-toggle px-3 py-2 text-primary rounded-lg font-medium transition-all duration-300 text-sm hover:bg-primary/10 border border-border"
              >
                {language === 'en' ? 'العربية' : 'English'}
              </button>

              {/* CTA Button */}
              <Link
                href="#contact"
                className="btn-primary px-5 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hidden sm:block text-sm"
              >
                {t('get-started', { en: 'Get Started', ar: 'ابدأ الآن' })}
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden text-primary hover:text-primary/80 transition-colors"
                aria-label="Toggle mobile menu"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div className={cn(
        'fixed top-0 right-0 w-80 h-full glass-effect-strong z-50 transition-transform duration-300 lg:hidden',
        isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
      )}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center space-x-3">
              <Image
                src="/images/logo-apcem.jpg"
                alt="APCEM Logo"
                width={32}
                height={32}
                className="rounded-lg"
              />
              <span className="text-xl font-bold gradient-text">APCEM</span>
            </div>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-primary hover:text-primary/80"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <div className="space-y-6">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className="block text-muted-foreground hover:text-primary font-medium py-2 transition-all duration-300 border-l-3 border-transparent hover:border-primary hover:pl-4"
              >
                {t('nav-' + link.href.replace('#', ''), link.label)}
              </Link>
            ))}

            <div className="pt-6 border-t border-border">
              <Link
                href="#contact"
                onClick={() => setIsMobileMenuOpen(false)}
                className="btn-primary text-black px-6 py-3 rounded-lg font-semibold w-full text-center block"
              >
                {t('get-started', { en: 'Get Started', ar: 'ابدأ الآن' })}
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  )
}

# 🚀 APCEM Agency - Next.js Installation Guide

## Prerequisites

- Node.js 18+ installed
- MySQL database (local or cloud)
- Git for version control

## 📦 Step 1: Install Dependencies

```bash
npm install
```

## 🔧 Step 2: Environment Setup

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Edit `.env.local` with your actual values:
```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/apcem_agency"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Email Configuration
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Admin Credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```

## 🗄️ Step 3: Database Setup

1. Create MySQL database:
```sql
CREATE DATABASE apcem_agency;
```

2. Generate Prisma client:
```bash
npx prisma generate
```

3. Push database schema:
```bash
npx prisma db push
```

4. (Optional) View database:
```bash
npx prisma studio
```

## 🖼️ Step 4: Add Your Logo

1. Place your logo file in `public/images/logo-apcem.jpg`
2. Ensure it's a square image (recommended: 512x512px)

## 🚀 Step 5: Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see your website!

## 📧 Step 6: Email Configuration (Optional)

For contact forms to work:

1. **Gmail Setup:**
   - Enable 2-factor authentication
   - Generate an app password
   - Use app password in `EMAIL_SERVER_PASSWORD`

2. **Other Email Providers:**
   - Update SMTP settings accordingly
   - Test with a simple email first

## 🔐 Step 7: Admin Access

1. Visit `/admin` (will be created in next phase)
2. Login with credentials from `.env.local`
3. Manage content, projects, and messages

## 🌐 Step 8: Production Deployment

### Vercel (Recommended)

1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Manual Deployment

```bash
npm run build
npm start
```

## 🎨 Customization

### Colors
Edit `tailwind.config.js` to change the gold color scheme:
```js
colors: {
  gold: {
    400: '#your-color',
    500: '#your-color',
    // ...
  }
}
```

### Content
- Update translations in component files
- Modify sections in `src/components/sections/`
- Add new pages in `src/app/`

### Fonts
- English: Inter (already configured)
- Arabic: Tajawal (already configured)
- Change in `src/app/layout.tsx`

## 🔧 Available Scripts

```bash
npm run dev          # Development server
npm run build        # Production build
npm run start        # Production server
npm run lint         # Code linting
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run migrations
npm run db:studio    # Database GUI
```

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Home page
│   ├── globals.css     # Global styles
│   ├── portfolio/      # Portfolio page
│   └── api/           # API routes
├── components/         # React components
│   ├── layout/        # Navigation, Footer
│   ├── sections/      # Page sections
│   ├── ui/           # UI components
│   ├── portfolio/    # Portfolio components
│   └── providers/    # Context providers
├── lib/               # Utilities
│   ├── db.ts         # Database connection
│   └── utils.ts      # Helper functions
└── types/            # TypeScript types
```

## ✅ Features Included

### Frontend
- ✅ Next.js 14 with App Router
- ✅ TypeScript for type safety
- ✅ Tailwind CSS styling
- ✅ Framer Motion animations
- ✅ Dark/Light mode toggle
- ✅ Arabic/English language support
- ✅ Responsive design
- ✅ Custom cursor effects
- ✅ Glass morphism design
- ✅ Portfolio page with filtering

### Backend Ready
- ✅ API routes structure
- ✅ Database schema (Prisma)
- ✅ Contact form API
- ✅ Email integration
- ✅ Environment configuration

### Next Phase (Admin Dashboard)
- [ ] Admin authentication
- [ ] Project management
- [ ] Content management
- [ ] Message handling
- [ ] File uploads
- [ ] Analytics

## 🆘 Troubleshooting

### Database Connection Issues
```bash
# Check database connection
npx prisma db pull

# Reset database
npx prisma migrate reset
```

### Build Errors
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### Email Not Working
- Check SMTP credentials
- Verify firewall settings
- Test with a simple email service first

## 📞 Support

- Email: <EMAIL>
- Documentation: README.md
- Issues: Create GitHub issue

## 🎯 Next Steps

1. ✅ **Current**: Frontend complete with API structure
2. 🔄 **Next**: Admin dashboard development
3. 📊 **Future**: Analytics and advanced features
4. 🚀 **Final**: Production deployment

Your Next.js full-stack APCEM Agency website is now ready for development! 🎉

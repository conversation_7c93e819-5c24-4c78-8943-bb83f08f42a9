'use client'

import Link from 'next/link'
import { useLanguage } from '@/components/providers/language-provider'

export function PortfolioSection() {
  const { t } = useLanguage()

  return (
    <section id="portfolio" className="py-32 relative">
      <div className="container mx-auto px-6">
        <div className="text-center">
          <h2 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
            {t('portfolio-title', { en: 'Our Work', ar: 'أعمالنا' })}
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto mb-12">
            {t('portfolio-description', {
              en: 'Discover our latest projects and creative solutions.',
              ar: 'اكتشف أحدث مشاريعنا وحلولنا الإبداعية.'
            })}
          </p>
          <Link
            href="/portfolio"
            className="btn-primary px-10 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 inline-block"
          >
            {t('view-all-projects', { en: 'View All Projects', ar: 'عرض جميع المشاريع' })}
          </Link>
        </div>
      </div>
    </section>
  )
}

'use client'

import { useState } from 'react'
import { useLanguage } from '@/components/providers/language-provider'
import { Mail, Phone, MapPin } from 'lucide-react'
import toast from 'react-hot-toast'

export function ContactSection() {
  const { t } = useLanguage()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    const formData = new FormData(e.currentTarget)
    const data = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      message: formData.get('message') as string,
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success(t('message-sent', { en: 'Message sent successfully!', ar: 'تم إرسال الرسالة بنجاح!' }))
        e.currentTarget.reset()
      } else {
        throw new Error('Failed to send message')
      }
    } catch (error) {
      toast.error(t('message-error', { en: 'Failed to send message', ar: 'فشل في إرسال الرسالة' }))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <section id="contact" className="py-32 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
            {t('contact-title', { en: 'Get In Touch', ar: 'تواصل معنا' })}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {t('contact-subtitle', {
              en: 'Ready to start your project? Let\'s discuss your ideas.',
              ar: 'مستعد لبدء مشروعك؟ دعنا نناقش أفكارك.'
            })}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Form */}
          <div className="glass-effect p-8 rounded-3xl">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <input
                  type="text"
                  name="name"
                  placeholder={t('name-placeholder', { en: 'Your Name', ar: 'اسمك' })}
                  required
                  className="w-full px-4 py-3 bg-background/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <input
                  type="email"
                  name="email"
                  placeholder={t('email-placeholder', { en: 'Your Email', ar: 'بريدك الإلكتروني' })}
                  required
                  className="w-full px-4 py-3 bg-background/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <textarea
                  name="message"
                  rows={5}
                  placeholder={t('message-placeholder', { en: 'Your Message', ar: 'رسالتك' })}
                  required
                  className="w-full px-4 py-3 bg-background/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                />
              </div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary w-full py-3 rounded-lg font-semibold transition-all duration-300 disabled:opacity-50"
              >
                {isSubmitting 
                  ? t('sending', { en: 'Sending...', ar: 'جاري الإرسال...' })
                  : t('send-message', { en: 'Send Message', ar: 'إرسال الرسالة' })
                }
              </button>
            </form>
          </div>

          {/* Contact Info */}
          <div className="space-y-8">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Mail className="w-6 h-6 text-black" />
              </div>
              <div>
                <div className="text-primary font-semibold">Email</div>
                <div className="text-muted-foreground"><EMAIL></div>
              </div>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Phone className="w-6 h-6 text-black" />
              </div>
              <div>
                <div className="text-primary font-semibold">
                  {t('phone', { en: 'Phone', ar: 'الهاتف' })}
                </div>
                <div className="text-muted-foreground">+****************</div>
              </div>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <MapPin className="w-6 h-6 text-black" />
              </div>
              <div>
                <div className="text-primary font-semibold">Instagram</div>
                <div className="text-muted-foreground">@apcem_agency</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

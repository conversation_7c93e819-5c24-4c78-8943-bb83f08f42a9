'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useLanguage } from '@/components/providers/language-provider'
import { ChevronDown } from 'lucide-react'
import { motion } from 'framer-motion'

export function HeroSection() {
  const { t } = useLanguage()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  const stats = [
    { number: '50+', label: { en: 'Projects', ar: 'مشروع' } },
    { number: '100%', label: { en: 'Satisfaction', ar: 'رضا العملاء' } },
    { number: '24/7', label: { en: 'Support', ar: 'الدعم' } },
    { number: '5+', label: { en: 'Years', ar: 'سنوات' } },
  ]

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden grid-overlay pt-20">
      {/* Particles Background */}
      <div className="particles absolute inset-0">
        {Array.from({ length: 9 }).map((_, i) => (
          <div
            key={i}
            className="particle w-2 h-2 bg-primary/20 rounded-full absolute animate-float"
            style={{
              left: `${10 + i * 10}%`,
              animationDelay: `${i * 2}s`,
              animationDuration: `${15 + i * 2}s`,
            }}
          />
        ))}
      </div>

      {/* Animated Background Blobs */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-primary/20 to-primary/30 rounded-full mix-blend-multiply filter blur-3xl opacity-20 morphing-blob floating-element" />
        <div className="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-primary/25 to-primary/35 rounded-full mix-blend-multiply filter blur-3xl opacity-25 morphing-blob floating-element" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-gradient-to-br from-primary/15 to-primary/25 rounded-full mix-blend-multiply filter blur-3xl opacity-15 morphing-blob floating-element" style={{ animationDelay: '4s' }} />
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mb-12"
          >
            <Image
              src="/images/logo-apcem.jpg"
              alt="APCEM Agency Logo"
              width={120}
              height={120}
              className="mx-auto rounded-2xl object-cover shadow-2xl hover:scale-105 hover:rotate-2 transition-all duration-500"
              priority
            />
          </motion.div>

          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black mb-4 leading-tight">
              <span className="block mb-2">
                {t('hero-title-1', { en: 'APCEM', ar: 'أبسيم' })}
              </span>
              <span className="gradient-text block">
                {t('hero-title-2', { en: 'AGENCY', ar: 'وكالة' })}
              </span>
            </h1>

            {/* Decorative Line */}
            <div className="w-32 h-1 bg-gradient-to-r from-primary to-primary/60 mx-auto rounded-full" />
          </motion.div>

          {/* Subtitle */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mb-12"
          >
            <p className="text-xl md:text-2xl lg:text-3xl text-muted-foreground max-w-5xl mx-auto leading-relaxed">
              {t('hero-subtitle', {
                en: 'Crafting Premium Digital Experiences with Cutting-Edge Innovation',
                ar: 'نصنع تجارب رقمية متميزة بأحدث التقنيات المبتكرة'
              })}
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mb-16"
          >
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                href="/portfolio"
                className="btn-primary px-12 py-5 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl"
              >
                {t('explore-work', { en: 'Explore Our Work', ar: 'استكشف أعمالنا' })}
              </Link>
              <Link
                href="#contact"
                className="neon-border text-primary px-12 py-5 rounded-xl font-bold text-lg hover:bg-primary hover:text-black transition-all duration-300 transform hover:scale-105 relative overflow-hidden"
              >
                {t('contact-us', { en: 'Contact Us', ar: 'تواصل معنا' })}
              </Link>
            </div>
          </motion.div>

          {/* Stats Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16"
          >
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-muted-foreground text-sm md:text-base">
                  {t(`stat-${index}`, stat.label)}
                </div>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
        >
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse" />
          </div>
          <ChevronDown className="w-4 h-4 text-primary mx-auto mt-2" />
        </motion.div>
      </div>
    </section>
  )
}

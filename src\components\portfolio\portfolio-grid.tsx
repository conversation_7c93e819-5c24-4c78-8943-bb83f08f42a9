'use client'

import { useState } from 'react'
import Image from 'next/image'
import { useLanguage } from '@/components/providers/language-provider'
import { ExternalLink } from 'lucide-react'

const portfolioProjects = [
  {
    id: 1,
    title: { en: "E-Commerce Platform", ar: "منصة التجارة الإلكترونية" },
    description: { en: "Modern online store with advanced features", ar: "متجر إلكتروني حديث بميزات متقدمة" },
    category: "web",
    technologies: ["React", "Node.js", "MongoDB"],
    image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop",
    year: "2024"
  },
  {
    id: 2,
    title: { en: "Corporate Website", ar: "موقع الشركة" },
    description: { en: "Professional business presence online", ar: "حضور مهني للأعمال عبر الإنترنت" },
    category: "web",
    technologies: ["Next.js", "TypeScript", "Prisma"],
    image: "https://images.unsplash.com/photo-*************-afdab827c52f?w=600&h=400&fit=crop",
    year: "2024"
  },
  {
    id: 3,
    title: { en: "Mobile Banking App", ar: "تطبيق البنك المحمول" },
    description: { en: "Secure mobile banking solution", ar: "حل مصرفي محمول آمن" },
    category: "mobile",
    technologies: ["React Native", "Firebase", "Redux"],
    image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop",
    year: "2024"
  },
  {
    id: 4,
    title: { en: "Brand Identity Design", ar: "تصميم الهوية التجارية" },
    description: { en: "Complete brand identity package", ar: "حزمة هوية تجارية كاملة" },
    category: "design",
    technologies: ["Illustrator", "Figma", "Photoshop"],
    image: "https://images.unsplash.com/photo-**********-2526d30994b5?w=600&h=400&fit=crop",
    year: "2024"
  },
  {
    id: 5,
    title: { en: "Restaurant Management", ar: "نظام إدارة المطاعم" },
    description: { en: "Complete restaurant management solution", ar: "حل إدارة مطاعم شامل" },
    category: "web",
    technologies: ["Vue.js", "Laravel", "MySQL"],
    image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop",
    year: "2023"
  },
  {
    id: 6,
    title: { en: "Fitness Tracking App", ar: "تطبيق تتبع اللياقة" },
    description: { en: "Comprehensive fitness tracking app", ar: "تطبيق تتبع لياقة شامل" },
    category: "mobile",
    technologies: ["Flutter", "Dart", "Firebase"],
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop",
    year: "2023"
  }
]

export function PortfolioGrid() {
  const { t, language } = useLanguage()
  const [activeFilter, setActiveFilter] = useState('all')
  const [displayedProjects, setDisplayedProjects] = useState(6)

  const filters = [
    { key: 'all', label: { en: 'All Projects', ar: 'جميع المشاريع' } },
    { key: 'web', label: { en: 'Web Development', ar: 'تطوير المواقع' } },
    { key: 'mobile', label: { en: 'Mobile Apps', ar: 'تطبيقات الجوال' } },
    { key: 'design', label: { en: 'Design', ar: 'التصميم' } }
  ]

  const filteredProjects = activeFilter === 'all' 
    ? portfolioProjects 
    : portfolioProjects.filter(project => project.category === activeFilter)

  const projectsToShow = filteredProjects.slice(0, displayedProjects)

  return (
    <section className="py-32 relative">
      <div className="container mx-auto px-6">
        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {filters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => {
                setActiveFilter(filter.key)
                setDisplayedProjects(6)
              }}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                activeFilter === filter.key
                  ? 'btn-primary text-black'
                  : 'glass-effect text-muted-foreground hover:text-primary border border-border'
              }`}
            >
              {t(`filter-${filter.key}`, filter.label)}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {projectsToShow.map((project) => (
            <div
              key={project.id}
              className="group glass-effect rounded-3xl overflow-hidden hover:scale-105 transition-all duration-500 relative"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              
              <div className="h-64 relative overflow-hidden">
                <Image
                  src={project.image}
                  alt={language === 'ar' ? project.title.ar : project.title.en}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20" />
                <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <ExternalLink className="w-6 h-6 text-white" />
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.slice(0, 3).map((tech, index) => (
                      <span key={index} className="px-2 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-3 text-primary group-hover:text-primary/80 transition-colors">
                  {language === 'ar' ? project.title.ar : project.title.en}
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {language === 'ar' ? project.description.ar : project.description.en}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{project.year}</span>
                  <button className="text-primary hover:text-primary/80 transition-colors group-hover:translate-x-2 transform duration-300">
                    <ExternalLink className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {displayedProjects < filteredProjects.length && (
          <div className="text-center mt-16">
            <button
              onClick={() => setDisplayedProjects(prev => prev + 3)}
              className="btn-primary px-10 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105"
            >
              {t('load-more', { en: 'Load More Projects', ar: 'تحميل المزيد من المشاريع' })}
            </button>
          </div>
        )}
      </div>
    </section>
  )
}

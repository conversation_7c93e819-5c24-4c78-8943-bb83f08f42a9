'use client'

import Image from 'next/image'
import { useLanguage } from '@/components/providers/language-provider'
import { motion } from 'framer-motion'

export function PortfolioHero() {
  const { t } = useLanguage()

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden grid-overlay pt-20">
      {/* Main Content */}
      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mb-12"
          >
            <Image
              src="/images/logo-apcem.jpg"
              alt="APCEM Agency Logo"
              width={120}
              height={120}
              className="mx-auto rounded-2xl object-cover shadow-2xl"
              priority
            />
          </motion.div>

          {/* Main Heading */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black mb-4 leading-tight">
              <span className="block mb-2">
                {t('portfolio-hero-title-1', { en: 'OUR', ar: 'معرض' })}
              </span>
              <span className="gradient-text block">
                {t('portfolio-hero-title-2', { en: 'PORTFOLIO', ar: 'أعمالنا' })}
              </span>
            </h1>

            {/* Decorative Line */}
            <div className="w-32 h-1 bg-gradient-to-r from-primary to-primary/60 mx-auto rounded-full" />
          </motion.div>

          {/* Subtitle */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mb-12"
          >
            <p className="text-xl md:text-2xl lg:text-3xl text-muted-foreground max-w-5xl mx-auto leading-relaxed">
              {t('portfolio-hero-subtitle', {
                en: 'Discover Our Latest Projects and Creative Solutions',
                ar: 'اكتشف أحدث مشاريعنا وحلولنا الإبداعية'
              })}
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

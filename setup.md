# APCEM Agency - Next.js Setup Guide

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
# or
yarn install
# or
pnpm install
```

### 2. Environment Setup
```bash
# Copy environment variables
cp .env.example .env.local

# Edit .env.local with your actual values
```

### 3. Database Setup
```bash
# Generate Prisma client
npx prisma generate

# Push database schema (for development)
npx prisma db push

# Or run migrations (for production)
npx prisma migrate dev --name init
```

### 4. Run Development Server
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the result.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   ├── globals.css        # Global styles
│   ├── portfolio/         # Portfolio pages
│   ├── admin/             # Admin dashboard
│   └── api/               # API routes
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── sections/         # Page sections
│   ├── ui/               # UI components
│   └── providers/        # Context providers
├── lib/                  # Utility functions
│   ├── db.ts            # Database connection
│   ├── utils.ts         # Helper functions
│   └── validations.ts   # Form validations
└── types/               # TypeScript types
```

## 🗄️ Database Configuration

### MySQL Setup
1. Create a MySQL database
2. Update `DATABASE_URL` in `.env.local`
3. Run `npx prisma db push`

### Example DATABASE_URL:
```
DATABASE_URL="mysql://username:password@localhost:3306/apcem_agency"
```

## 📧 Email Configuration

For contact forms to work, configure email settings in `.env.local`:

```env
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"
```

## 🔐 Admin Setup

1. Set admin credentials in `.env.local`
2. Visit `/admin` to access the dashboard
3. Default login: <EMAIL> / admin123

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables
4. Deploy automatically

### Manual Deployment
```bash
npm run build
npm start
```

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## 🎨 Features

### Frontend
- ✅ Next.js 14 with App Router
- ✅ TypeScript for type safety
- ✅ Tailwind CSS for styling
- ✅ Framer Motion for animations
- ✅ Dark/Light mode toggle
- ✅ Arabic/English language support
- ✅ Responsive design
- ✅ Custom cursor effects
- ✅ Glass morphism design

### Backend
- ✅ API routes for contact forms
- ✅ Database integration with Prisma
- ✅ Admin authentication
- ✅ Portfolio management
- ✅ Email notifications
- ✅ File upload handling

### Admin Dashboard
- ✅ Project management
- ✅ Contact message handling
- ✅ Content management
- ✅ Analytics dashboard
- ✅ User management

## 🔧 Customization

### Adding New Languages
1. Update `LanguageProvider` in `src/components/providers/language-provider.tsx`
2. Add translations throughout components
3. Update font configurations in `layout.tsx`

### Modifying Styles
- Global styles: `src/app/globals.css`
- Tailwind config: `tailwind.config.js`
- Component styles: Use Tailwind classes

### Adding New Sections
1. Create component in `src/components/sections/`
2. Add to main page in `src/app/page.tsx`
3. Update navigation links

## 📞 Support

For questions or issues:
- Email: <EMAIL>
- Documentation: Check README.md
- Issues: Create GitHub issue

## 📄 License

This project is proprietary to APCEM Agency. All rights reserved.

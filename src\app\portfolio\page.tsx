import { Metadata } from 'next'
import { Navigation } from '@/components/layout/navigation'
import { Footer } from '@/components/layout/footer'
import { CursorFollower } from '@/components/ui/cursor-follower'
import { ScrollProgress } from '@/components/ui/scroll-progress'
import { BackgroundElements } from '@/components/ui/background-elements'
import { PortfolioHero } from '@/components/portfolio/portfolio-hero'
import { PortfolioGrid } from '@/components/portfolio/portfolio-grid'

export const metadata: Metadata = {
  title: 'Portfolio - APCEM Agency',
  description: 'Discover our latest projects and creative solutions. Web development, design, and digital marketing work.',
}

export default function PortfolioPage() {
  return (
    <main className="min-h-screen bg-background text-foreground overflow-x-hidden">
      {/* Background Elements */}
      <BackgroundElements />
      
      {/* Cursor Follower */}
      <CursorFollower />
      
      {/* Scroll Progress */}
      <ScrollProgress />
      
      {/* Navigation */}
      <Navigation />
      
      {/* Main Content */}
      <div className="relative z-10">
        <PortfolioHero />
        <PortfolioGrid />
      </div>
      
      {/* Footer */}
      <Footer />
    </main>
  )
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}
::-webkit-scrollbar-track {
    background: #000;
    border-radius: 10px;
}
::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706);
    border-radius: 10px;
    border: 2px solid #000;
}
::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #f59e0b, #d97706, #b45309);
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

/* Firefox Scrollbar */
html {
    scrollbar-width: thin;
    scrollbar-color: #fbbf24 #000;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}
@keyframes glow {
    from { box-shadow: 0 0 20px #f59e0b, 0 0 30px rgba(251, 191, 36, 0.3); }
    to { box-shadow: 0 0 30px #fbbf24, 0 0 40px #fbbf24, 0 0 50px rgba(251, 191, 36, 0.5); }
}
@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
@keyframes morphing {
    0%, 100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
    50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
}

/* Text and Effects */
.gradient-text {
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
}

.glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-effect-strong {
    backdrop-filter: blur(30px) saturate(200%);
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(251, 191, 36, 0.4);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.neon-border {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #000, #000) padding-box,
                linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24) border-box;
    animation: shimmer 3s ease-in-out infinite;
}

.morphing-blob {
    animation: morphing 8s ease-in-out infinite;
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

/* Fonts */
.ar-font { font-family: 'Tajawal', sans-serif; }
.en-font { font-family: 'Inter', sans-serif; }

/* RTL Layout Fixes */
[dir="rtl"] .flex {
    flex-direction: row-reverse;
}
[dir="rtl"] .space-x-3 > * + * {
    margin-left: 0;
    margin-right: 0.75rem;
}
[dir="rtl"] .space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}
[dir="rtl"] .space-x-6 > * + * {
    margin-left: 0;
    margin-right: 1.5rem;
}
[dir="rtl"] .space-x-8 > * + * {
    margin-left: 0;
    margin-right: 2rem;
}
[dir="rtl"] .text-left {
    text-align: right;
}
[dir="rtl"] .text-right {
    text-align: left;
}

/* Enhanced button styles */
.btn-primary {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Particle background */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: #fbbf24;
    border-radius: 50%;
    opacity: 0.1;
    animation: float 20s infinite linear;
}

/* Grid pattern overlay */
.grid-overlay {
    background-image: 
        linear-gradient(rgba(251, 191, 36, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(251, 191, 36, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
}

/* Modern Navigation */
.modern-nav {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-logo {
    transition: all 0.3s ease;
}

.nav-logo:hover {
    transform: scale(1.05);
}

.nav-link {
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Language Toggle Button */
.lang-toggle {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(251, 191, 36, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.lang-toggle:hover {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.6);
    transform: translateY(-2px);
}

.lang-toggle.active {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    color: #000;
}

/* Logo styles */
.logo-main {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(251, 191, 36, 0.2);
    transition: all 0.5s ease;
}

.logo-main:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(251, 191, 36, 0.4);
}

.logo-nav {
    width: 45px;
    height: 45px;
    object-fit: cover;
    border-radius: 12px;
}

/* Mobile menu */
.mobile-menu {
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-nav-link {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    padding-left: 1rem;
}

.mobile-nav-link:hover {
    border-left-color: #fbbf24;
    background: rgba(251, 191, 36, 0.1);
    transform: translateX(8px);
}

/* Enhanced navigation styles */
.nav-logo {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    object-fit: cover;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.2);
}

.nav-logo:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
}

.nav-link {
    position: relative;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

.nav-link:hover {
    background: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
}

/* Language Toggle Enhanced */
.lang-toggle {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(251, 191, 36, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.lang-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.2), transparent);
    transition: left 0.5s;
}

.lang-toggle:hover::before {
    left: 100%;
}

.lang-toggle:hover {
    background: rgba(251, 191, 36, 0.1);
    border-color: #fbbf24;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.2);
}

.lang-toggle.active {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    color: #000;
    border-color: transparent;
    transform: scale(0.95);
}

/* Loading states */
body {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.loaded {
    opacity: 1;
}

/* Enhanced hover effects */
.glass-effect:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(251, 191, 36, 0.3);
}

.gradient-text:hover {
    animation: glow 1s ease-in-out infinite alternate;
}

/* Trail animation */
@keyframes trailFade {
    to {
        opacity: 0;
        transform: scale(0);
    }
}
